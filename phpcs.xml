<?xml version="1.0"?>
<ruleset name="Custom Standard">
  <description>A custom coding standard</description>

  <rule ref="PSR2"/>

  <rule ref="Generic.Files.LineLength">
    <properties>
      <property name="lineLimit" value="120"/>
      <property name="absoluteLineLimit" value="0"/>
    </properties>
  </rule>

  <rule ref="Squiz.NamingConventions.ValidVariableName"/>

  <rule ref="Squiz.NamingConventions.ValidVariableName.PublicHasUnderscore">
    <severity>0</severity>
  </rule>
  <rule ref="Squiz.NamingConventions.ValidVariableName.PrivateNoUnderscore">
    <severity>0</severity>
  </rule>
</ruleset>
