worker_processes auto;

events {
	worker_connections 512;
}

http {
	include /etc/nginx/mime.types;
	client_max_body_size 100M;

	server {
		listen 80;
		root /srv;
		server_name localhost;

		location / {
			index index.html index.php;
			try_files $uri $uri/ /index.php?$query_string;
		}

		location ~ \.php$ {
			include fastcgi_params;

			fastcgi_index index.php;
			fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
			fastcgi_pass example:80;
			proxy_redirect off;
			proxy_set_header Connection "upgrade";
			proxy_set_header Host $http_host;
			proxy_set_header Upgrade $http_upgrade;
			proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
			proxy_set_header X-Forwarded-Host $http_x_forwarded_host;
			proxy_set_header X-Forwarded-Proto $http_x_forwarded_proto;
			proxy_set_header X-Real-IP $remote_addr;
		}
	}

	server {
		listen 8080;
		server_name localhost;

		location / {
			client_max_body_size 100m;
			proxy_http_version 1.1;
			proxy_pass http://documentserver;
			proxy_redirect off;
			proxy_set_header Connection "upgrade";
			proxy_set_header Host $http_host;
			proxy_set_header Upgrade $http_upgrade;
			proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
			proxy_set_header X-Forwarded-Host $http_x_forwarded_host;
			proxy_set_header X-Forwarded-Proto $http_x_forwarded_proto;
			proxy_set_header X-Real-IP $remote_addr;
		}
	}
}
