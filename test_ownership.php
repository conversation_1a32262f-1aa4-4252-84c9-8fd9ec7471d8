<?php
/**
 * Test script pour vérifier la fonctionnalité de vérification de propriété de fichier
 */

require_once 'src/functions.php';
require_once 'src/trackmanager.php';
require_once 'src/helpers/ExampleUsers.php';

use Example\Helpers\ExampleUsers;

// Simuler un environnement de test
function testFileOwnership() {
    echo "=== Test de vérification de propriété de fichier ===\n";
    
    // Créer un fichier de test
    $testFileName = "test_document.docx";
    $testUserAddress = "127.0.0.1";
    $ownerId = "uid-1";
    $nonOwnerId = "uid-2";
    
    // Créer le répertoire de stockage si nécessaire
    $storagePath = getStoragePath($testFileName, $testUserAddress);
    $storageDir = dirname($storagePath);
    if (!is_dir($storageDir)) {
        mkdir($storageDir, 0755, true);
    }
    
    // Créer un fichier de test
    file_put_contents($storagePath, "Test content");
    
    // Créer les métadonnées pour le fichier
    createMeta($testFileName, $ownerId, "Test Owner", $testUserAddress);
    
    echo "Fichier créé: $testFileName\n";
    echo "Propriétaire: $ownerId\n";
    
    // Test 1: Vérifier que le propriétaire peut accéder
    echo "\n--- Test 1: Propriétaire légitime ---\n";
    $canAccess = checkFileOwnership($testFileName, $ownerId, $testUserAddress);
    echo "Résultat: " . ($canAccess ? "ACCÈS AUTORISÉ" : "ACCÈS REFUSÉ") . "\n";
    
    // Test 2: Vérifier qu'un non-propriétaire ne peut pas accéder
    echo "\n--- Test 2: Non-propriétaire ---\n";
    $canAccess = checkFileOwnership($testFileName, $nonOwnerId, $testUserAddress);
    echo "Résultat: " . ($canAccess ? "ACCÈS AUTORISÉ" : "ACCÈS REFUSÉ") . "\n";
    
    // Test 3: Tester avec un utilisateur null
    echo "\n--- Test 3: Utilisateur null ---\n";
    $canAccess = checkFileOwnership($testFileName, null, $testUserAddress);
    echo "Résultat: " . ($canAccess ? "ACCÈS AUTORISÉ" : "ACCÈS REFUSÉ") . "\n";
    
    // Nettoyer
    unlink($storagePath);
    $histDir = getHistoryDir($storagePath);
    if (is_dir($histDir)) {
        delTree($histDir);
    }
    
    echo "\n=== Tests terminés ===\n";
}

// Fonction helper pour supprimer un répertoire et son contenu
function delTree($dir) {
    if (!file_exists($dir) || !is_dir($dir)) {
        return;
    }
    
    $files = array_diff(scandir($dir), ['.', '..']);
    foreach ($files as $file) {
        (is_dir("$dir/$file")) ? delTree("$dir/$file") : unlink("$dir/$file");
    }
    return rmdir($dir);
}

// Exécuter les tests
if (php_sapi_name() === 'cli') {
    testFileOwnership();
} else {
    echo "<pre>";
    testFileOwnership();
    echo "</pre>";
}
?>
