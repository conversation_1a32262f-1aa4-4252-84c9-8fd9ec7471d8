version: "3.8"

services:
  documentserver:
    container_name: documentserver
    image: onlyoffice/documentserver:8.2
    expose:
      - "80"
    environment:
      - JWT_SECRET=your-256-bit-secret

  example:
    container_name: example
    build:
      context: .
      target: example
    expose:
      - "80"
    volumes:
      - "example:/srv"
      - ./src:/srv/src
      - ./index.php:/srv/index.php
      - ./assets:/srv/assets
      - ./templates:/srv/templates
    environment:
      - ADDRESS=0.0.0.0
      - DOCUMENT_SERVER_PRIVATE_URL=http://proxy:8080
      - DOCUMENT_SERVER_PUBLIC_URL=http://localhost:8080
      - EXAMPLE_URL=http://proxy
      - JWT_SECRET=your-256-bit-secret
      - PORT=80

  proxy:
    container_name: proxy
    build:
      context: .
      target: proxy
    depends_on:
      - documentserver
      - example
    ports:
      - "80:80"
      - "8080:8080"
    volumes:
      - "example:/srv"

volumes:
  example:
