<?php
//
// (c) Copyright Ascensio System SIA 2025
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

namespace Example\Format;

final class Format
{
    public string $name;

    public string $type;

    /**
     * @var string[]
     */
    public array $actions;

    /**
     * @var string[]
     */
    public array $convert;

    /**
     * @var string[]
     */
    public array $mime;

    public function extension(): string
    {
        // In PHP, unlike other languages, when parsing a path, the extension is
        // returned without a dot.
        //
        // ```php
        // pathinfo('file.docx', PATHINFO_EXTENSION) === 'docx'
        // ```
        return $this->name;
    }
}
